# 示例代码部分（已注释掉）
# # example.py
# import ddddocr
#
# ocr = ddddocr.DdddOcr()
#
# image = open("example.jpg", "rb").read()
# result = ocr.classification(image)
# print(result)


# 导入所需的库
import ddddocr  # 导入 ddddocr 库，用于图像识别/验证码识别
import base64   # 导入 base64 库，用于处理 base64 编码的图片数据

# 创建 ddddocr 实例对象
# DdddOcr() 支持多种参数配置，这里使用默认配置
ocr = ddddocr.DdddOcr()

# base64_str 变量用于存储图片的 base64 编码字符串
# 注意：使用时需要去掉 base64 字符串开头的 'data:image/png;base64,' 部分
# 这里需要填入完整的 base64 字符串
base64_str = ""  # 在这里填入你的 base64 字符串

# 将 base64 字符串解码为字节数据
img_bytes = base64.b64decode(base64_str)

# 使用 ddddocr 进行图像识别
# classification 方法接收图片字节数据，返回识别结果
result = ocr.classification(img_bytes)

# 输出识别结果
print("识别结果:", result)